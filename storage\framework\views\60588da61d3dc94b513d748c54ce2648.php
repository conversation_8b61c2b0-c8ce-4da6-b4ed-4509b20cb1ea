<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Flowmaker</title>
    <meta name="description" content="Flowmaker" />
    <meta name="author" content="Flowmaker" />
    <meta property="og:image" content="/og-image.png" />
    <link rel="stylesheet" href="<?php echo e(url('/flowmaker/css')); ?>">

  </head>

  <body>
    <script>
      window.baseUrl = "<?php echo e(url('/')); ?>";
      window.data = JSON.parse(<?php echo json_encode($data, 15, 512) ?>);
    </script>

    
    
<div id="flow" data='<?php echo e($data); ?>'></div>

<!-- Patch for API requests to use correct base URL -->
<script>
  // Store the original fetch function
  const originalFetch = window.fetch;
  
  // Override the fetch function
  window.fetch = function(url, options) {
    // Check if the URL starts with '/ai/' or equals '/flowmakermedia' and doesn't already have the base URL
    if (typeof url === 'string' && (url.startsWith('/ai/') || url === '/flowmakermedia') && !url.startsWith('http')) {
      // Replace the URL with the full URL including base path
      url = window.baseUrl + url;
      console.log('Modified URL:', url);
      
      // Log additional info for debugging DELETE requests
      if (options && options.method === 'DELETE') {
        console.log('DELETE request intercepted:', { url, options });
      }
    }
    // Call the original fetch with the potentially modified URL
    return originalFetch.call(this, url, options);
  };
</script>

<script src="<?php echo e(url('/flowmaker/script')); ?>"></script>



</body>
</html><?php /**PATH C:\xampp\htdocs\zaptra\modules\Flowmaker\Providers/../Resources/views/index.blade.php ENDPATH**/ ?>